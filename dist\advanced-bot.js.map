{"version": 3, "file": "advanced-bot.js", "sourceRoot": "", "sources": ["../src/advanced-bot.ts"], "names": [], "mappings": ";;;;;AAAA,uCAAqD;AAErD,oDAA4B;AAE5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAShB,MAAM,mBAAmB;IAIvB;QAFQ,iBAAY,GAAqB,IAAI,GAAG,EAAE,CAAC;QAGjD,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;QAEpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,mBAAQ,CAAa,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,YAAY;QAClB,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;YAC5B,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBACnC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBACpC,CAAC;gBACD,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa;QACnB,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACrB,MAAM,QAAQ,GAAG,iBAAM,CAAC,cAAc,CAAC;gBACrC,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC3C,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC3C,CAAC,CAAC;YAEH,GAAG,CAAC,KAAK,CACP,wCAAwC;gBACxC,mBAAmB;gBACnB,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,QAAQ,EACR,QAAQ,CACT,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;YACjC,GAAG,CAAC,OAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;YAClC,GAAG,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,QAAQ;QACR,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;YAC/B,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAChE,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC;oBACH,kCAAkC;oBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACzC,GAAG,CAAC,KAAK,CAAC,YAAY,UAAU,MAAM,MAAM,EAAE,CAAC,CAAC;gBAClD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;YACjC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvE,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAExC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC/B,GAAG,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBAC7D,OAAO;YACT,CAAC;YAED,GAAG,CAAC,KAAK,CAAC,YAAY,OAAO,YAAY,OAAO,GAAG,CAAC,CAAC;YAErD,UAAU,CAAC,GAAG,EAAE;gBACd,GAAG,CAAC,KAAK,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;YACjC,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;YAC1B,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;YAE9B,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,WAAW;YACrB,CAAC;YAED,WAAW;YACX,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACjC,OAAO;YACT,CAAC;YAED,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc;QACpB,QAAQ;QACR,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YAC/B,GAAG,CAAC,eAAe,CACjB,gBAAgB;gBAChB,4CAA4C;gBAC5C,QAAQ;gBACR,gBAAgB;gBAChB,iBAAiB;gBACjB,eAAe;gBACf,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,SAAS,EACT,iBAAM,CAAC,cAAc,CAAC;gBACpB,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;aAC7C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE;YACtC,GAAG,CAAC,eAAe,CACjB,cAAc;gBACd,uBAAuB;gBACvB,yBAAyB;gBACzB,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,SAAS,EACT,iBAAM,CAAC,cAAc,CAAC;gBACpB,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;aAC7C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,EAAE;YACxC,GAAG,CAAC,eAAe,CACjB,eAAe;gBACf,+BAA+B;gBAC/B,kBAAkB;gBAClB,WAAW;gBACX,YAAY;gBACZ,WAAW;gBACX,SAAS,EACT,iBAAM,CAAC,cAAc,CAAC;gBACpB,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;aAC7C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,EAAE;YACxC,GAAG,CAAC,eAAe,CACjB,YAAY;gBACZ,6BAA6B;gBAC7B,4BAA4B;gBAC5B,mBAAmB,EACnB,iBAAM,CAAC,cAAc,CAAC;gBACpB,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;aAC7C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,GAAe;QAClC,MAAM,QAAQ,GAAG,iBAAM,CAAC,cAAc,CAAC;YACrC,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAClD,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;YACrD,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACpD,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3C,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,yBAAyB,CAAC;QAE1C,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;YACtB,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,GAAe,EAAE,IAAY;QACpD,MAAM,OAAO,GAAG,GAAG,CAAC,OAAQ,CAAC;QAE7B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,aAAa;gBAChB,OAAO,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;gBAE5B,MAAM,WAAW,GAAG,iBAAM,CAAC,cAAc,CAAC;oBACxC,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC9C,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC9C,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC9C,CAAC,iBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;iBAC/C,CAAC,CAAC;gBAEH,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,gBAAgB,EAAE,WAAW,CAAC,CAAC;gBACvD,MAAM;YAER,KAAK,iBAAiB;gBACpB,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC7B,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;gBAEzB,GAAG,CAAC,KAAK,CACP,cAAc;oBACd,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI;oBAC5B,QAAQ,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI;oBAC5B,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,MAAM;oBAClC,WAAW,CACZ,CAAC;gBACF,MAAM;QACV,CAAC;QAED,WAAW;QACX,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE;YAClC,MAAM,GAAG,GAAG,GAAG,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC5C,GAAG,CAAC,OAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YAC5B,GAAG,CAAC,OAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC;YAEtC,GAAG,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,GAAe,EAAE,IAAY;QACrD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9E,GAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpF,GAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,QAAQ,CAAC,UAAkB;QACjC,sBAAsB;QACtB,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,QAAQ,CAAC,yBAAyB,SAAS,GAAG,CAAC,EAAE,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAEtC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAExB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAErC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAEtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,IAAI,CAAC,MAAc;QACzB,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,gBAAgB,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;CACF;AAED,kBAAe,mBAAmB,CAAC"}