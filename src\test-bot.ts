import TelegramBot from './bot';

// 简单的测试脚本，用于验证机器人配置
async function testBot() {
  console.log('🧪 开始测试机器人配置...');
  
  try {
    // 检查环境变量
    if (!process.env.BOT_TOKEN) {
      console.error('❌ BOT_TOKEN 环境变量未设置');
      console.log('请在 .env 文件中设置您的机器人 token');
      process.exit(1);
    }

    console.log('✅ 环境变量检查通过');
    
    // 创建机器人实例
    const bot = new TelegramBot();
    console.log('✅ 机器人实例创建成功');
    
    console.log('🎉 所有测试通过！');
    console.log('');
    console.log('现在您可以运行以下命令启动机器人：');
    console.log('  npm run dev        # 基础机器人（开发模式）');
    console.log('  npm run dev:advanced # 高级机器人（开发模式）');
    console.log('');
    console.log('或者构建后运行：');
    console.log('  npm run build');
    console.log('  npm start          # 基础机器人');
    console.log('  npm run start:advanced # 高级机器人');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testBot();
