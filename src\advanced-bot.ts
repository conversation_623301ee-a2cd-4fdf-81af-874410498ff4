import { Telegraf, Context, Markup } from 'telegraf';
import { Update } from 'telegraf/typings/core/types/typegram';
import dotenv from 'dotenv';

dotenv.config();

interface BotContext extends Context<Update> {
  session?: {
    step?: string;
    data?: any;
  };
}

class AdvancedTelegramBot {
  private bot: Telegraf<BotContext>;
  private userSessions: Map<number, any> = new Map();

  constructor() {
    const token = process.env.BOT_TOKEN;
    
    if (!token) {
      throw new Error('BOT_TOKEN 环境变量未设置');
    }

    this.bot = new Telegraf<BotContext>(token);
    this.setupSession();
    this.setupCommands();
    this.setupCallbacks();
  }

  private setupSession() {
    // 简单的会话管理
    this.bot.use((ctx, next) => {
      const userId = ctx.from?.id;
      if (userId) {
        if (!this.userSessions.has(userId)) {
          this.userSessions.set(userId, {});
        }
        ctx.session = this.userSessions.get(userId);
      }
      return next();
    });
  }

  private setupCommands() {
    // 开始命令
    this.bot.start((ctx) => {
      const keyboard = Markup.inlineKeyboard([
        [Markup.button.callback('📊 功能菜单', 'menu')],
        [Markup.button.callback('ℹ️ 关于', 'about')],
      ]);

      ctx.reply(
        '🚀 欢迎使用高级 TypeScript Telegram 机器人！\n\n' +
        '这个机器人展示了更多高级功能：\n' +
        '• 内联键盘\n' +
        '• 会话管理\n' +
        '• 多步对话\n' +
        '• 文件处理\n' +
        '• 定时任务',
        keyboard
      );
    });

    // 菜单命令
    this.bot.command('menu', (ctx) => {
      this.showMainMenu(ctx);
    });

    // 调查问卷命令
    this.bot.command('survey', (ctx) => {
      ctx.session!.step = 'survey_name';
      ctx.reply('📝 让我们开始一个简单的调查！\n\n请告诉我您的姓名：');
    });

    // 计算器命令
    this.bot.command('calc', (ctx) => {
      const expression = ctx.message.text.replace('/calc', '').trim();
      if (expression) {
        try {
          // 简单的数学表达式计算（注意：在生产环境中应该使用更安全的方法）
          const result = this.safeEval(expression);
          ctx.reply(`🧮 计算结果: ${expression} = ${result}`);
        } catch (error) {
          ctx.reply('❌ 无效的数学表达式');
        }
      } else {
        ctx.reply('请输入数学表达式，例如: /calc 2 + 3 * 4');
      }
    });

    // 提醒命令
    this.bot.command('remind', (ctx) => {
      const args = ctx.message.text.replace('/remind', '').trim().split(' ');
      const seconds = parseInt(args[0]);
      const message = args.slice(1).join(' ');

      if (isNaN(seconds) || !message) {
        ctx.reply('用法: /remind <秒数> <提醒内容>\n例如: /remind 60 喝水时间到了！');
        return;
      }

      ctx.reply(`⏰ 好的！我会在 ${seconds} 秒后提醒您: "${message}"`);

      setTimeout(() => {
        ctx.reply(`🔔 提醒: ${message}`);
      }, seconds * 1000);
    });

    // 处理文本消息（会话管理）
    this.bot.on('text', (ctx) => {
      const text = ctx.message.text;

      if (text.startsWith('/')) {
        return; // 让命令处理器处理
      }

      // 处理调查问卷会话
      if (ctx.session?.step) {
        this.handleSurveyStep(ctx, text);
        return;
      }

      // 默认文本处理
      this.handleDefaultText(ctx, text);
    });
  }

  private setupCallbacks() {
    // 主菜单回调
    this.bot.action('menu', (ctx) => {
      this.showMainMenu(ctx);
    });

    // 关于回调
    this.bot.action('about', (ctx) => {
      ctx.editMessageText(
        '🤖 关于这个机器人\n\n' +
        '这是一个用 TypeScript 编写的高级 Telegram 机器人示例。\n\n' +
        '技术栈:\n' +
        '• TypeScript\n' +
        '• Telegraf.js\n' +
        '• Node.js\n\n' +
        '功能特性:\n' +
        '• 内联键盘交互\n' +
        '• 会话状态管理\n' +
        '• 多步对话流程\n' +
        '• 定时提醒功能\n' +
        '• 数学计算器',
        Markup.inlineKeyboard([
          [Markup.button.callback('🔙 返回主菜单', 'menu')]
        ])
      );
    });

    // 功能按钮回调
    this.bot.action('feature_calc', (ctx) => {
      ctx.editMessageText(
        '🧮 计算器功能\n\n' +
        '使用方法: /calc <数学表达式>\n' +
        '例如: /calc 2 + 3 * 4\n\n' +
        '支持的运算:\n' +
        '• 加法 (+)\n' +
        '• 减法 (-)\n' +
        '• 乘法 (*)\n' +
        '• 除法 (/)\n' +
        '• 括号 ()',
        Markup.inlineKeyboard([
          [Markup.button.callback('🔙 返回主菜单', 'menu')]
        ])
      );
    });

    this.bot.action('feature_survey', (ctx) => {
      ctx.editMessageText(
        '📝 调查问卷功能\n\n' +
        '使用 /survey 命令开始一个简单的调查问卷。\n\n' +
        '机器人会引导您完成以下步骤:\n' +
        '1. 输入姓名\n' +
        '2. 选择年龄段\n' +
        '3. 输入反馈\n' +
        '4. 查看结果',
        Markup.inlineKeyboard([
          [Markup.button.callback('🔙 返回主菜单', 'menu')]
        ])
      );
    });

    this.bot.action('feature_remind', (ctx) => {
      ctx.editMessageText(
        '⏰ 提醒功能\n\n' +
        '使用方法: /remind <秒数> <提醒内容>\n' +
        '例如: /remind 60 喝水时间到了！\n\n' +
        '机器人会在指定时间后发送提醒消息。',
        Markup.inlineKeyboard([
          [Markup.button.callback('🔙 返回主菜单', 'menu')]
        ])
      );
    });
  }

  private showMainMenu(ctx: BotContext) {
    const keyboard = Markup.inlineKeyboard([
      [Markup.button.callback('🧮 计算器', 'feature_calc')],
      [Markup.button.callback('📝 调查问卷', 'feature_survey')],
      [Markup.button.callback('⏰ 提醒功能', 'feature_remind')],
      [Markup.button.callback('ℹ️ 关于', 'about')]
    ]);

    const message = '📊 功能菜单\n\n请选择您想要使用的功能：';

    if (ctx.callbackQuery) {
      ctx.editMessageText(message, keyboard);
    } else {
      ctx.reply(message, keyboard);
    }
  }

  private handleSurveyStep(ctx: BotContext, text: string) {
    const session = ctx.session!;

    switch (session.step) {
      case 'survey_name':
        session.data = { name: text };
        session.step = 'survey_age';
        
        const ageKeyboard = Markup.inlineKeyboard([
          [Markup.button.callback('18-25', 'age_18_25')],
          [Markup.button.callback('26-35', 'age_26_35')],
          [Markup.button.callback('36-45', 'age_36_45')],
          [Markup.button.callback('45+', 'age_45_plus')]
        ]);
        
        ctx.reply(`很高兴认识您，${text}！\n\n请选择您的年龄段：`, ageKeyboard);
        break;

      case 'survey_feedback':
        session.data.feedback = text;
        session.step = undefined;
        
        ctx.reply(
          '📋 调查完成！\n\n' +
          `姓名: ${session.data.name}\n` +
          `年龄段: ${session.data.age}\n` +
          `反馈: ${session.data.feedback}\n\n` +
          '感谢您的参与！🎉'
        );
        break;
    }

    // 处理年龄选择回调
    this.bot.action(/age_(.+)/, (ctx) => {
      const age = ctx.match![1].replace('_', '-');
      ctx.session!.data.age = age;
      ctx.session!.step = 'survey_feedback';
      
      ctx.editMessageText('请分享您对这个机器人的反馈或建议：');
    });
  }

  private handleDefaultText(ctx: BotContext, text: string) {
    if (text.toLowerCase().includes('你好') || text.toLowerCase().includes('hello')) {
      ctx.reply('你好！👋 使用 /menu 查看所有功能。');
    } else if (text.toLowerCase().includes('帮助') || text.toLowerCase().includes('help')) {
      ctx.reply('需要帮助吗？使用 /menu 查看所有可用功能！');
    } else {
      ctx.reply(`我收到了您的消息: "${text}"\n\n使用 /menu 查看所有功能。`);
    }
  }

  private safeEval(expression: string): number {
    // 简单的数学表达式计算（仅支持基本运算）
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
    if (sanitized !== expression) {
      throw new Error('Invalid characters in expression');
    }
    return Function(`"use strict"; return (${sanitized})`)();
  }

  public async start() {
    try {
      console.log('正在启动高级 Telegram 机器人...');
      
      await this.bot.launch();
      
      console.log('✅ 高级 Telegram 机器人已启动！');
      
      process.once('SIGINT', () => this.stop('SIGINT'));
      process.once('SIGTERM', () => this.stop('SIGTERM'));
      
    } catch (error) {
      console.error('启动机器人时出错:', error);
      process.exit(1);
    }
  }

  private stop(signal: string) {
    console.log(`\n收到 ${signal} 信号，正在关闭机器人...`);
    this.bot.stop(signal);
    console.log('机器人已关闭');
    process.exit(0);
  }
}

export default AdvancedTelegramBot;
