"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const bot_1 = __importDefault(require("./bot"));
async function main() {
    try {
        const bot = new bot_1.default();
        await bot.start();
    }
    catch (error) {
        console.error('应用程序启动失败:', error);
        process.exit(1);
    }
}
// 启动应用程序
main();
//# sourceMappingURL=index.js.map