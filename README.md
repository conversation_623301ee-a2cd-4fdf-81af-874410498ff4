# TypeScript Telegram Bot 示例

这是一个使用 TypeScript 编写的 Telegram 机器人示例项目，展示了如何创建功能丰富的 Telegram 机器人。

## 功能特性

### 基础机器人 (`src/bot.ts`)
- ✅ 基本命令处理 (`/start`, `/help`, `/echo`, `/time`, `/random`)
- ✅ 天气查询示例 (`/weather`)
- ✅ 随机笑话 (`/joke`)
- ✅ 智能文本回复
- ✅ 多媒体消息处理（贴纸、照片、文档）
- ✅ 错误处理和日志记录
- ✅ 优雅关闭

### 高级机器人 (`src/advanced-bot.ts`)
- ✅ 内联键盘交互
- ✅ 会话状态管理
- ✅ 多步对话流程（调查问卷）
- ✅ 数学计算器功能
- ✅ 定时提醒功能
- ✅ 动态菜单系统

## 技术栈

- **TypeScript** - 类型安全的 JavaScript
- **Telegraf.js** - 现代 Telegram Bot 框架
- **Node.js** - JavaScript 运行时
- **dotenv** - 环境变量管理

## 快速开始

### 1. 克隆项目并安装依赖

```bash
# 安装依赖
npm install
```

### 2. 创建 Telegram 机器人

1. 在 Telegram 中找到 [@BotFather](https://t.me/botfather)
2. 发送 `/newbot` 命令
3. 按照提示设置机器人名称和用户名
4. 获取机器人 token

### 3. 配置环境变量

复制 `.env.example` 到 `.env` 并填入你的机器人 token：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
BOT_TOKEN=your_bot_token_here
PORT=3000
```

### 4. 运行机器人

#### 开发模式（推荐）
```bash
# 运行基础机器人
npm run dev

# 运行高级机器人
npm run dev:advanced
```

#### 生产模式
```bash
# 构建项目
npm run build

# 运行基础机器人
npm start

# 运行高级机器人
npm run start:advanced
```

## 项目结构

```
├── src/
│   ├── bot.ts          # 基础机器人实现
│   ├── advanced-bot.ts # 高级机器人实现
│   ├── index.ts        # 基础机器人入口
│   └── advanced.ts     # 高级机器人入口
├── dist/               # 编译输出目录
├── .env                # 环境变量配置
├── .env.example        # 环境变量示例
├── tsconfig.json       # TypeScript 配置
├── package.json        # 项目配置
└── README.md          # 项目说明
```

## 可用命令

### 基础机器人命令
- `/start` - 显示欢迎消息和命令列表
- `/help` - 显示帮助信息
- `/echo <消息>` - 回显用户消息
- `/time` - 显示当前时间
- `/random` - 生成随机数 (1-100)
- `/weather <城市>` - 天气查询示例
- `/joke` - 随机程序员笑话

### 高级机器人命令
- `/start` - 显示欢迎消息和功能菜单
- `/menu` - 显示功能菜单
- `/survey` - 开始调查问卷
- `/calc <表达式>` - 数学计算器
- `/remind <秒数> <消息>` - 设置提醒

## 开发指南

### 添加新命令

在 `setupCommands()` 方法中添加新的命令处理器：

```typescript
this.bot.command('newcommand', (ctx) => {
  ctx.reply('这是一个新命令！');
});
```

### 添加内联键盘

```typescript
import { Markup } from 'telegraf';

const keyboard = Markup.inlineKeyboard([
  [Markup.button.callback('按钮文本', 'callback_data')],
]);

ctx.reply('选择一个选项：', keyboard);
```

### 处理回调查询

```typescript
this.bot.action('callback_data', (ctx) => {
  ctx.answerCbQuery();
  ctx.reply('按钮被点击了！');
});
```

## 部署

### 使用 PM2 部署

```bash
# 安装 PM2
npm install -g pm2

# 构建项目
npm run build

# 启动机器人
pm2 start dist/index.js --name "telegram-bot"

# 查看状态
pm2 status

# 查看日志
pm2 logs telegram-bot
```

### 使用 Docker 部署

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

构建和运行：

```bash
docker build -t telegram-bot .
docker run -d --env-file .env telegram-bot
```

## 注意事项

1. **Token 安全**: 永远不要将机器人 token 提交到版本控制系统
2. **错误处理**: 生产环境中要有完善的错误处理和日志记录
3. **速率限制**: 注意 Telegram API 的速率限制
4. **会话管理**: 大量用户时考虑使用 Redis 等外部存储管理会话

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
