{"name": "telegram-bot-typescript", "version": "1.0.0", "description": "TypeScript Telegram Bot Example", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "start:advanced": "node dist/advanced.js", "dev": "nodemon --exec ts-node src/index.ts", "dev:advanced": "nodemon --exec ts-node src/advanced.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "ts-node src/test-bot.ts", "test:build": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["telegram", "bot", "typescript", "nodejs"], "author": "", "license": "ISC", "dependencies": {"dotenv": "^17.2.1", "telegraf": "^4.16.3"}, "devDependencies": {"@types/node": "^24.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}