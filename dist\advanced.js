"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const advanced_bot_1 = __importDefault(require("./advanced-bot"));
async function main() {
    try {
        const bot = new advanced_bot_1.default();
        await bot.start();
    }
    catch (error) {
        console.error('高级机器人启动失败:', error);
        process.exit(1);
    }
}
// 启动高级机器人
main();
//# sourceMappingURL=advanced.js.map