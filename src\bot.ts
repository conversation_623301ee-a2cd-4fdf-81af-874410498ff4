import { Telegraf, Context } from 'telegraf';
import { Update } from 'telegraf/typings/core/types/typegram';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 扩展 Context 类型以添加自定义属性
interface BotContext extends Context<Update> {
  // 可以在这里添加自定义属性
}

class TelegramBot {
  private bot: Telegraf<BotContext>;

  constructor() {
    const token = process.env.BOT_TOKEN;
    
    if (!token) {
      throw new Error('BOT_TOKEN 环境变量未设置');
    }

    this.bot = new Telegraf<BotContext>(token);
    this.setupCommands();
    this.setupMiddleware();
  }

  private setupMiddleware() {
    // 日志中间件
    this.bot.use((ctx, next) => {
      const start = Date.now();
      console.log(`收到消息: ${ctx.message ? JSON.stringify(ctx.message) : '非消息更新'}`);
      
      return next().then(() => {
        const ms = Date.now() - start;
        console.log(`处理时间: ${ms}ms`);
      });
    });

    // 错误处理中间件
    this.bot.catch((err, ctx) => {
      console.error('机器人错误:', err);
      ctx.reply('抱歉，处理您的请求时出现了错误。');
    });
  }

  private setupCommands() {
    // 开始命令
    this.bot.start((ctx) => {
      const welcomeMessage = `
🤖 欢迎使用 TypeScript Telegram 机器人！

可用命令：
/start - 显示欢迎消息
/help - 显示帮助信息
/echo <消息> - 回显您的消息
/time - 显示当前时间
/random - 生成随机数
/weather <城市> - 获取天气信息（示例）
/joke - 随机笑话
      `;
      ctx.reply(welcomeMessage);
    });

    // 帮助命令
    this.bot.help((ctx) => {
      ctx.reply('这是一个用 TypeScript 编写的 Telegram 机器人示例。使用 /start 查看所有可用命令。');
    });

    // 回显命令
    this.bot.command('echo', (ctx) => {
      const message = ctx.message.text.replace('/echo', '').trim();
      if (message) {
        ctx.reply(`🔄 您说: ${message}`);
      } else {
        ctx.reply('请在 /echo 后面输入要回显的消息');
      }
    });

    // 时间命令
    this.bot.command('time', (ctx) => {
      const now = new Date();
      const timeString = now.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      ctx.reply(`🕐 当前时间: ${timeString}`);
    });

    // 随机数命令
    this.bot.command('random', (ctx) => {
      const randomNum = Math.floor(Math.random() * 100) + 1;
      ctx.reply(`🎲 随机数: ${randomNum}`);
    });

    // 天气命令（示例）
    this.bot.command('weather', (ctx) => {
      const city = ctx.message.text.replace('/weather', '').trim();
      if (city) {
        // 这里可以集成真实的天气 API
        ctx.reply(`🌤️ ${city} 的天气: 晴朗，25°C\n（这是一个示例响应，请集成真实的天气 API）`);
      } else {
        ctx.reply('请输入城市名称，例如: /weather 北京');
      }
    });

    // 笑话命令
    this.bot.command('joke', (ctx) => {
      const jokes = [
        '为什么程序员喜欢黑暗？因为光会产生 bug！',
        '程序员的三大美德：懒惰、急躁和傲慢。',
        '为什么程序员总是混淆圣诞节和万圣节？因为 Oct 31 == Dec 25！',
        '世界上有 10 种人：懂二进制的和不懂二进制的。',
        '程序员：我写的代码没有 bug。测试员：Hold my beer...'
      ];
      const randomJoke = jokes[Math.floor(Math.random() * jokes.length)];
      ctx.reply(`😄 ${randomJoke}`);
    });

    // 处理所有文本消息
    this.bot.on('text', (ctx) => {
      const text = ctx.message.text;
      
      // 如果不是命令，提供一些智能回复
      if (!text.startsWith('/')) {
        if (text.toLowerCase().includes('你好') || text.toLowerCase().includes('hello')) {
          ctx.reply('你好！👋 很高兴见到你！');
        } else if (text.toLowerCase().includes('谢谢') || text.toLowerCase().includes('thank')) {
          ctx.reply('不客气！😊');
        } else if (text.toLowerCase().includes('再见') || text.toLowerCase().includes('bye')) {
          ctx.reply('再见！👋 期待下次见面！');
        } else {
          ctx.reply(`我收到了您的消息: "${text}"\n\n使用 /help 查看可用命令。`);
        }
      }
    });

    // 处理贴纸
    this.bot.on('sticker', (ctx) => {
      ctx.reply('很棒的贴纸！🎨');
    });

    // 处理照片
    this.bot.on('photo', (ctx) => {
      ctx.reply('很棒的照片！📸');
    });

    // 处理文档
    this.bot.on('document', (ctx) => {
      ctx.reply('我收到了您的文档！📄');
    });
  }

  public async start() {
    try {
      console.log('正在启动 Telegram 机器人...');
      
      // 启动机器人
      await this.bot.launch();
      
      console.log('✅ Telegram 机器人已启动！');
      
      // 优雅关闭
      process.once('SIGINT', () => this.stop('SIGINT'));
      process.once('SIGTERM', () => this.stop('SIGTERM'));
      
    } catch (error) {
      console.error('启动机器人时出错:', error);
      process.exit(1);
    }
  }

  private stop(signal: string) {
    console.log(`\n收到 ${signal} 信号，正在关闭机器人...`);
    this.bot.stop(signal);
    console.log('机器人已关闭');
    process.exit(0);
  }
}

export default TelegramBot;
